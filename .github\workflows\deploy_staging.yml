# https://docs.github.com/en/actions/use-cases-and-examples/deploying/deploying-to-amazon-elastic-container-service

name: STAGING - Build and publish Docker image to AWS ECR

on:
  push:
    branches: [ staging ]

jobs:
  build-and-push:
    name: STAGING - Build and push Docker image to AWS ECR
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    env:
      ECR_REPOSITORY: ${{ secrets.AWS_ECR_REPOSITORY }}
      IMAGE_TAG: latest  # Change this to a specific version if needed eg. 1.0.0, production, etc.
      GIT_SHA: ${{ github.sha }}
      LAMBDA_FUNCTION_NAME: ${{ secrets.AWS_LAMBDA_FUNCTION_STAGING }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          # Build a docker container and
          # push it to ECR so that it can
          # be deployed to ECS.
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Update Lambda function with new image
        run: |
          aws lambda update-function-code \
            --function-name $LAMBDA_FUNCTION_NAME \
            --image-uri $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG 
