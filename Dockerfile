# set base image (host OS)
FROM public.ecr.aws/lambda/python:3.11

# optional: ensure pip is up to date
RUN /var/lang/bin/python3.11 -m pip install --upgrade pip

# install dependencies - copy the dependencies file to the working directory
COPY ./requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy the content of the local scripts directory to the working directory
COPY ./app .

# command to run on container start
CMD ["main.handler"]
