import os
import logging
from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import awswrangler.secretsmanager as sm

os.environ.setdefault("AWS_REGION", "eu-central-1")

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s : %(levelname)s : %(message)s"
)
logger = logging.getLogger(__name__)


def init_sentry():
    """
    Initialise the Sentry SDK for error tracking.
    """
    import sentry_sdk
    from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN"),
        integrations=[SqlalchemyIntegration()],
        send_default_pii=True,
        traces_sample_rate=1.0,
        _experiments={
            "continuous_profiling_auto_start": True,
        },
    )


class SecretsManager:
    """Secrets Manager class."""

    def __init__(self):
        self.secret_name = "staging/cropsense-backend-ml-api-secrets" if os.getenv("ENV") == "dev" else "prod/cropsense-backend-ml-api-secrets"
        self.environment = "AWS" if os.getenv("ENV") == "dev" else "AWS"

    def get_aws_secrets(self):
        """Get a secret from AWS Secrets Manager."""
        return sm.get_secret_json(self.secret_name)

    def get_local_secrets(self):
        """Get a secret from local environment."""
        return {
            "ENV": os.getenv("ENV"),
            "DB_URI": os.getenv("DB_URI"),
            "DB_SCHEMA": os.getenv("DB_SCHEMA"),
            "JWT_SECRET_KEY": os.getenv("JWT_SECRET_KEY"),
            "JWT_REFRESH_SECRET_KEY": os.getenv("JWT_REFRESH_SECRET_KEY"),
        }

    def get_secrets(self):
        """Get secrets."""
        if self.environment == "LOCAL":
            return self.get_local_secrets()
        else:
            return self.get_aws_secrets()

class Database:
    """Database class."""

    def __init__(self):
        self.engine = create_engine(secrets.get("DB_URI"))
        self.session = sessionmaker(
            bind=self.engine,
            expire_on_commit=False,
            autoflush=False
        )()

    def get_session(self):
        """Get a database session."""
        return self.session

    def get_engine(self):
        """Get a database engine."""
        return self.engine


# Load secrets and database session
secrets = SecretsManager().get_secrets()
db = Database().get_session()
