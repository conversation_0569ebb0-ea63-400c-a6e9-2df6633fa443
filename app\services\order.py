from fastapi import HTTPException, status
from datetime import datetime
from sqlalchemy.orm import Session
from models import Order, OrderItem, OrganizationItem
from schemas.order import OrderCreate, OrderUpdate
from config import db
from decimal import Decimal

class OrderService:
    def get_order_by_id(self, order_id: str) -> Order:
        """
        Get a single order by ID.

        Args:
            order_id (str): The ID of the order to get.

        Raises:
            HTTPException: If the order is not found (404).

        Returns:
            Order: The order, if found.
        """
        db_order = db.query(Order).filter(Order.order_id == order_id).first()
        if not db_order:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Order not found.")
        db_order.items = db.query(OrderItem).filter(OrderItem.order_id == db_order.order_id).all()
        return db_order

    def get_orders(self, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a list of orders.

        Args:
            skip (int, optional): The number of orders to skip. Defaults to 0.
            limit (int, optional): The maximum number of orders to return. Defaults to 100.

        Returns:
            dict: A dictionary containing the list of orders and the total count.
        """
        orders = db.query(Order).offset(skip).limit(limit).all()
        for order in orders:
            order.items = db.query(OrderItem).filter(OrderItem.order_id == order.order_id).all()
        total = db.query(Order).count()
        return {"orders": orders, "total": total}

    def get_orders_by_user_id(self, user_id: str, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a list of orders by user ID.

        Args:
            user_id (str): The ID of the user.
            skip (int, optional): The number of orders to skip. Defaults to 0.
            limit (int, optional): The maximum number of orders to return. Defaults to 100.

        Returns:
            dict: A dictionary containing the list of orders and the total count.
        """
        orders = db.query(Order).filter(Order.buyer_id == user_id).offset(skip).limit(limit).all()
        for order in orders:
            order.items = db.query(OrderItem).filter(OrderItem.order_id == order.order_id).all()
        total = db.query(Order).filter(Order.buyer_id == user_id).count()
        return {"orders": orders, "total": total}

    def create_order(self, order: OrderCreate, user_id: str) -> Order:
        """
        Create a new order.

        Args:
            order (OrderCreate): The data to create the order with.
            user_id (str): The ID of the user creating the order.

        Raises:
            HTTPException: If any of the items in the order are not found (404) or if there is an error creating the order (500).

        Returns:
            Order: The created order.
        """
        try:
            sub_total = Decimal('0.00')
            vat_total = Decimal('0.00')
            order_items = []

            for item_data in order.items:
                db_item = db.query(OrganizationItem).filter(OrganizationItem.item_id == item_data.item_id).first()
                if not db_item:
                    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Item with id {item_data.item_id} not found.")

                item_sub_total = db_item.item_price * item_data.quantity
                # TODO: Add logic for VAT calculation
                
                sub_total += item_sub_total

                order_item = OrderItem(
                    item_id=item_data.item_id,
                    quantity=item_data.quantity,
                    unit_price=db_item.item_price,
                    sub_total=item_sub_total,
                    seller_id=db_item.organization_id,
                    item_type=db_item.item_type,
                )
                order_items.append(order_item)

            total_amount = sub_total + vat_total

            db_order = Order(
                buyer_id=user_id,
                sub_total=sub_total,
                vat_total=vat_total,
                total_amount=total_amount,
                order_status="pending",
            )

            db.add(db_order)
            db.commit()
            db.refresh(db_order)

            for order_item in order_items:
                order_item.order_id = db_order.order_id
                db.add(order_item)
            
            db.commit()
            db.refresh(db_order)

            return db_order
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error creating order: {e}")

    def update_order(self, order_id: str, order: OrderUpdate) -> Order:
        """
        Update an existing order.

        Args:
            order_id (str): The ID of the order to update.
            order (OrderUpdate): The data to update the order with.

        Raises:
            HTTPException: If the order is not found (404) or if there is an error updating the order (500).

        Returns:
            Order: The updated order.
        """
        try:
            db_order = self.get_order_by_id(order_id)
            update_data = order.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_order, key, value)
            db_order.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_order)
            return db_order
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error updating order: {e}")

    def delete_order(self, order_id: str) -> Order:
        """
        Soft delete an order by setting is_active to False.

        Args:
            order_id (str): The ID of the order to delete.

        Returns:
            Order: The updated order.
        """
        try:
            db_order = self.get_order_by_id(order_id)
            db_order.is_active = False
            db_order.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_order)
            return db_order
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error deleting order: {e}")
