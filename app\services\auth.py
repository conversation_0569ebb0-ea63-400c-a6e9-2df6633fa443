from typing import Annotated, <PERSON>
from datetime import datetime, timedelta, timezone

from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer

from config import db, secrets, logger
from models import Users as UserModel
from schemas.users import UserInDB, TokenData

# JWT
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 120  # 120mins
JWT_SECRET_KEY = secrets.get("JWT_SECRET_KEY")
JWT_REFRESH_SECRET_KEY = secrets.get("JWT_REFRESH_SECRET_KEY")

# OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """Auth services"""

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a stored password against one provided by user"""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password for storing."""
        return pwd_context.hash(password)

    @staticmethod
    def check_if_password_is_valid(password: str) -> bool:
        """Check if the password is valid

        Hint: Password must be at least 8 characters long and contain at least one digit, one uppercase and one lowercase letter
        """
        return (
                len(password) >= 8
                and password.lower() != password
                and password.upper() != password
                and not password.isalnum()
                and any(i.isdigit() for i in password)
        )

    @staticmethod
    def get_user(email: str) -> UserInDB:
        """Get a user from the database"""
        return db.query(UserModel).filter(UserModel.email == email).first()

    @staticmethod
    def authenticate_user(email: str, password: str) -> UserInDB:
        """Authenticate a user"""
        if user := AuthService.get_user(email):
            return user if AuthService.verify_password(password, user.hashed_password) else False
        else:
            return False

    @staticmethod
    def create_access_token(data: dict, expires_delta: Union[timedelta, None] = None) -> str:
        """Create an JWT access token"""
        try:
            to_encode = data.copy()
            if expires_delta:
                expire = datetime.now(timezone.utc) + expires_delta
            else:
                expire = datetime.now(timezone.utc) + \
                         timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

            to_encode["exp"] = expire
            return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
        except Exception as e:
            logger.error(f"Error creating access token: {e}")
            return False

    @staticmethod
    def create_token_from_email(email: str, expires_delta: Union[timedelta, None] = None) -> str:
        """Create a JWT access token using only email
        
        Args:
            email (str): The email address to create token for
            expires_delta (Union[timedelta, None], optional): Custom expiration time. Defaults to None.
            
        Returns:
            str: JWT token string or False if error occurs
        """
        try:
            data = {"sub": email}
            return AuthService.create_access_token(data, expires_delta)
        except Exception as e:
            logger.error(f"Error creating token from email: {e}")
            return False


async def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]) -> UserInDB:
    """Get the current user from the token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")

        if email is None:
            raise credentials_exception

        token_data = TokenData(email=email)
    except JWTError as e:
        logger.error(f"Error decoding token: {e}")
        raise credentials_exception from e

    user = AuthService.get_user(token_data.email)
    if user is None:
        raise credentials_exception

    return user


async def get_current_active_user(current_user: Annotated[UserInDB, Depends(get_current_user)]) -> UserInDB:
    """Get the current active user from the token"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
