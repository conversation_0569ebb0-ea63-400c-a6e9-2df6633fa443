from pydantic import BaseModel, EmailStr, HttpUrl
from uuid import UUID
from datetime import datetime
from typing import Optional, List


class OrganizationCreate(BaseModel):
    organization_name: str
    organization_description: str
    contact_email: EmailStr
    contact_phone: str
    website: str


class OrganizationUpdate(BaseModel):
    organization_name: Optional[str] = None
    organization_description: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    website: Optional[str] = None


class Organization(BaseModel):
    organization_id: str
    organization_name: str
    organization_description: str
    contact_email: EmailStr
    contact_phone: str
    website: str
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True
   
        
class OrganizationList(BaseModel):
       organizations: List[Organization]
       total: int
       
       class Config:
            from_attributes = True