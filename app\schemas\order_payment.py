from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from enum import Enum


class PaymentStatus(Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class PaymentMethod(Enum):
    CREDIT_CARD = "credit_card"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"


class OrderPaymentCreate(BaseModel):
    order_id: str
    payment_method: PaymentMethod
    amount: Decimal
    transaction_id: Optional[str] = None


class OrderPaymentUpdate(BaseModel):
    payment_status: Optional[PaymentStatus] = None
    transaction_id: Optional[str] = None


class OrderPayment(BaseModel):
    payment_id: str
    order_id: str
    payment_status: PaymentStatus
    payment_method: PaymentMethod
    amount: Decimal
    transaction_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True