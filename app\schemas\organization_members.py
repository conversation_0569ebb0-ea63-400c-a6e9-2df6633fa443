from pydantic import BaseModel
from uuid import UUID
from datetime import datetime
from typing import Optional, List


class OrganizationMemberCreate(BaseModel):
    organization_id: str
    user_id: str
    role: str


class OrganizationMemberUpdate(BaseModel):
    role: Optional[str] = None
    accept_invite: Optional[bool] = None
    is_active: Optional[bool] = None


class OrganizationMember(BaseModel):
    member_id: str
    organization_id: str
    user_id: str
    role: str
    accept_invite: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OrganizationMemberList(BaseModel):
    organization_members: List[OrganizationMember]
    total: int

    class Config:
        from_attributes = True
