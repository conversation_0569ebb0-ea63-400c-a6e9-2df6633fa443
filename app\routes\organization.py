from fastapi import APIRouter, Depends, Request
from uuid import UUID
from schemas.organization import Organization, OrganizationCreate, OrganizationUpdate, OrganizationList
from schemas.users import User
from services.organization import OrganizationService
from services.auth import get_current_active_user
from services.api_log import ApiLogsService

router = APIRouter()
current_user = Depends(get_current_active_user)

@router.post(
    "/create", 
    response_model=Organization, 
    summary="Create a new organization"
)
def create_organization(
    data: OrganizationCreate, 
     current_user: User = current_user,
):
    """
    Create a new organization.
    """
    response = OrganizationService().create_organization(organization=data)
    return response

@router.get(
    "/", 
    response_model=OrganizationList, 
    summary="Read a list of organizations"
)
def read_organizations(
    skip: int = 0, 
    limit: int = 100, 
     current_user: User = current_user,
):
    """
    Read a list of organizations.
    """
    response = OrganizationService().get_organizations(skip=skip, limit=limit)
    return response

@router.get(
    "/{org_id}", 
    response_model=Organization, 
    summary="Read a single organization"
)
def read_organization(
    organization_id: str, 
     current_user: User = current_user,
):
    """
    Read a single organization.
    """
    db_org = OrganizationService().get_organization_by_id(organization_id=organization_id)
    return db_org

@router.put(
    "/{org_id}", 
    response_model=Organization, 
    summary="Update an organization"
)
def update_organization(
    org_id: str, 
    org: OrganizationUpdate, 
     current_user: User = current_user,
):
    """
    Update an organization.
    """
    db_org = OrganizationService().update_organization(organization_id=org_id, organization=org)
    return db_org

@router.delete(
    "/{org_id}", 
    response_model=None, 
    summary="Delete an organization"
)
def delete_organization(
    org_id: str, 
     current_user: User = current_user,
):
    """
    Delete an organization.
    """
    db_org = OrganizationService().delete_organization(organization_id=org_id)
    return db_org