from fastapi import HTTPException, status

from config import db, logger
from models import ApiLogs as ApiLogsModel


class ApiLogsService:
    @staticmethod
    def log_request(
        user_id: None,
        ip_address: str,
        path: str,
        request_body: dict,
        response_body: dict
    ):
        """Log API request and response"""
        try:
            # Log request details
            api_log = ApiLogsModel(
                user_id=user_id,
                ip_address=ip_address,
                path=path,
                request_body=request_body,
                response_body=response_body
            )
            db.add(api_log)
            db.commit()

            logger.info(f"API request logged: {api_log}")
        except Exception as e:
            db.rollback()
            
            logger.error(f"Error logging API request: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error logging API request"
            )
