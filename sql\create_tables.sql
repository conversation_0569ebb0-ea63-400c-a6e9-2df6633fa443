-- SQL script to create tables based on the models in app/models.py

-- Drop schema if it exists
DROP SCHEMA IF EXISTS marketplace_dev CASCADE;

-- Create schema
CREATE SCHEMA marketplace_dev;

-- Create users table
CREATE TABLE marketplace_dev.users (
    user_id VARCHAR PRIMARY KEY NOT NULL,
    email VARCHAR UNIQUE NOT NULL,
    first_name VA<PERSON><PERSON>R NOT NULL,
    last_name VA<PERSON>HAR NOT NULL,
    hashed_password VARCHAR NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create marketplace_api_requests_logs table
CREATE TABLE marketplace_dev.marketplace_api_requests_logs (
    id SERIAL PRIMARY KEY NOT NULL,
    user_id VARCHAR NOT NULL,
    ip_address VARCHAR NOT NULL,
    path VARCHAR NOT NULL,
    request_body JSON,
    response_body JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organizations table
CREATE TABLE marketplace_dev.organizations (
    organization_id VARCHAR PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_name VARCHAR(100) UNIQUE NOT NULL,
    organization_description TEXT,
    contact_email VARCHAR(255) NOT NULL,
    contact_phone VARCHAR(20),
    website VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organization_items table
CREATE TABLE marketplace_dev.organization_items (
    item_id VARCHAR PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id VARCHAR NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_description TEXT NOT NULL,
    item_price NUMERIC(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'NGN',
    stock_quantity INTEGER DEFAULT 0,
    min_order_quantity INTEGER DEFAULT 1,
    item_type VARCHAR NOT NULL,
    pricing_model VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE marketplace_dev.orders (
    order_id VARCHAR PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_status VARCHAR NOT NULL,
    buyer_id VARCHAR NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'NGN',
    sub_total NUMERIC(10, 2) NOT NULL,
    vat_total NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    total_amount NUMERIC(10, 2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create order_items table
CREATE TABLE marketplace_dev.order_items (
    order_item_id SERIAL PRIMARY KEY,
    order_id VARCHAR NOT NULL,
    seller_id VARCHAR NOT NULL,
    item_id VARCHAR NOT NULL,
    item_type VARCHAR NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price NUMERIC(10, 2) NOT NULL,
    sub_total NUMERIC(10, 2) NOT NULL,
    vat NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organization_members table
CREATE TABLE marketplace_dev.organization_members (
    member_id VARCHAR PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id VARCHAR NOT NULL,
    user_id VARCHAR NOT NULL,
    role VARCHAR NOT NULL,
    accept_invite BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create order_payments table
CREATE TABLE marketplace_dev.order_payments (
    payment_id VARCHAR PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id VARCHAR NOT NULL,
    payment_status VARCHAR NOT NULL,
    payment_method VARCHAR NOT NULL,
    amount_paid NUMERIC(10, 2) NOT NULL,
    transaction_id VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_organization_name ON marketplace_dev.organizations (organization_name);
CREATE INDEX idx_item_name ON marketplace_dev.organization_items (item_name);
CREATE INDEX idx_buyer_id ON marketplace_dev.orders (buyer_id);
CREATE INDEX idx_item_id ON marketplace_dev.order_items (item_id);
CREATE INDEX idx_order_id ON marketplace_dev.order_payments (order_id);
