from mangum import Mangum
from fastapi import FastAPI, APIRouter
from starlette.responses import RedirectResponse

from config import secrets, init_sentry
from routes.auth import router as auth_router
from routes.organization import router as organization_router
from routes.order import router as orders_router
from routes.organization_item import router as organization_item_router
from routes.organization_members import router as organization_members_router
from routes.order_payment import router as order_payment_router

# Initialize Sentry
init_sentry()

app = FastAPI(
    title=f"CropSense Marketplace API",
    description=f"CropSense Marketplace API - {secrets['ENV'].upper()} Environment",
    version="v0.1.0",
    contact={
        "name": "CropSense Africa",
        "email": "<EMAIL>",
        "url": "https://cropsense.africa/"
    },
    root_path=f"/{secrets['ENV']}/" if secrets['ENV'] not in ['dev', 'prod'] else "/",
)
router = APIRouter()


@router.get("/")
async def root():
    return RedirectResponse("/docs")

app.include_router(router, include_in_schema=False)
app.include_router(auth_router, prefix="/auth", tags=["Auth"])
app.include_router(organization_router, prefix="/organizations", tags=["Organizations"])
app.include_router(orders_router, prefix="/orders", tags=["Orders"])
app.include_router(organization_item_router, prefix="/organization-items", tags=["Organization Items"])
app.include_router(organization_members_router, prefix="/organization-members", tags=["Organization Members"])
app.include_router(order_payment_router, prefix="/order-payments", tags=["Order Payments"])


handler = Mangum(app)

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, port=8000, host='0.0.0.0')
