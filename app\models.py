import os
import uuid
from datetime import datetime
from decimal import Decimal
from sqlalchemy import (
    Column,
    String,
    Text,
    JSON,
    Integer,
    Boolean,
    DateTime,
    Numeric,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
DB_SCHEMA = os.getenv("DB_SCHEMA") or "public"


Base = declarative_base()

class Users(Base):
    __tablename__ = 'users'
    __table_args__ = {'schema': DB_SCHEMA}
    
    user_id = Column(String, primary_key=True, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<User {self.email}>"

    def __str__(self):
        return f"{self.email}"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

  
class ApiLogs(Base):
    __tablename__ = 'marketplace_api_requests_logs'
    __table_args__ = {'schema': DB_SCHEMA}

    id = Column(Integer, primary_key=True, unique=True, nullable=False)
    user_id = Column(String, nullable=False)
    ip_address = Column(String, nullable=False)
    path = Column(String, nullable=False)
    request_body = Column(JSON, nullable=True)
    response_body = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<ApiLogs {self.id}>"
    
    def __str__(self):
        return f"{self.id}"
    

class Organization(Base):
    __tablename__ = "organizations"
    __table_args__ = {'schema': DB_SCHEMA}
    
    organization_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    organization_name = Column(String(100), unique=True, index=True, nullable=False)
    organization_description = Column(Text, nullable=True)
    contact_email = Column(String(255), nullable=False)
    contact_phone = Column(String(20), nullable=True)
    website = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<Organization {self.organization_name}>"
    
    def __str__(self):
        return f"{self.organization_name}"
 
    
class OrganizationItem(Base):
    __tablename__ = "organization_items"
    __table_args__ = {'schema': DB_SCHEMA}

    item_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    organization_id = Column(String, nullable=False)
    item_name = Column(String(200), nullable=False, index=True)
    item_description = Column(Text, nullable=False)
    item_price = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="NGN")
    stock_quantity = Column(Integer, nullable=True, default=0)
    min_order_quantity = Column(Integer, nullable=True, default=1)
    item_type = Column(String, nullable=False)
    pricing_model = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    create_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<OrganizationItem {self.item_name}>"
    
    def __str__(self):
        return f"{self.item_name}"


class Order(Base):
    __tablename__ = "orders"
    __table_args__ = {'schema': DB_SCHEMA}

    order_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    order_status = Column(String, nullable=False)
    buyer_id = Column(String, nullable=False, index=True) 
    currency = Column(String(3), nullable=False, default="NGN")
    sub_total = Column(Numeric(10, 2), nullable=False)
    vat_total = Column(Numeric(10, 2), nullable=False, default=Decimal('0.00'))
    total_amount = Column(Numeric(10, 2), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)  
    
    def __repr__(self):
        return f"<Order {self.order_id}>"
    
    def __str__(self):
        return f"{self.order_id}"  
    
    
class OrderItem(Base):
    __tablename__ = "order_items"
    __table_args__ = {'schema': DB_SCHEMA}
    
    order_item_id = Column(Integer, primary_key=True) 
    order_id = Column(String, nullable=False) 
    seller_id = Column(String, nullable=False) 
    item_id = Column(String, nullable=False, index=True)
    item_type = Column(String, nullable=False)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 2), nullable=False)
    sub_total = Column(Numeric(10, 2), nullable=False)
    vat = Column(Numeric(10, 2), nullable=False, default=Decimal('0.00'))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<OrderItem {self.order_item_id}>"
    
    def __str__(self):
        return f"{self.order_item_id}"



class OrganizationMember(Base):
    __tablename__ = "organization_members"
    __table_args__ = {'schema': DB_SCHEMA}
    
    member_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    organization_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    role = Column(String, nullable=False)
    accept_invite = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<OrganizationMember {self.member_id}>"
    
    def __str__(self):
        return f"{self.member_id}"


class OrderPayment(Base):
    __tablename__ = "order_payments"
    __table_args__ = {'schema': DB_SCHEMA}

    payment_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    order_id = Column(String, nullable=False, index=True)
    payment_status = Column(String, nullable=False)
    payment_method = Column(String, nullable=False)
    amount_paid = Column(Numeric(10, 2), nullable=False)
    transaction_id = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<OrderPayment {self.payment_id}>"

    def __str__(self):
        return f"{self.payment_id}"


# class ItemRating(Base):
#     pass