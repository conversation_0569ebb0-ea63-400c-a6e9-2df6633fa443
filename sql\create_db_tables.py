import os
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Import the Base object from your models file
from models import Base,DB_SCHEMA
from config import secrets

# The database URL you provided
# In a real application, you should load this from environment variables
DATABASE_URL = secrets["DB_URI"]

def create_tables():
    """
    Connects to the database and creates all tables
    defined in the SQLAlchemy Base metadata.
    """
    try:
        print("Connecting to the database...")
        engine = create_engine(DATABASE_URL)

        with engine.connect() as connection:
            print(f"Creating schema {DB_SCHEMA} if it does not exist...")
            connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {DB_SCHEMA}"))
            connection.commit()

        # This command creates all tables that inherit from Base
        print("Creating tables...")
        Base.metadata.create_all(bind=engine)

        print("Tables created successfully!")

    except SQLAlchemyError as e:
        print(f"An error occurred during table creation: {e}")
    except ImportError as e:
        print(f"Error importing models: {e}")
        print("Please ensure you are running this script from the root of your project directory.")

if __name__ == "__main__":
    create_tables()
