from fastapi import HTTPException, status
from datetime import datetime
from sqlalchemy.orm import Session
from uuid import UUID
from models import Organization
from schemas.organization import OrganizationCreate, OrganizationUpdate
from config import db

class OrganizationService:
    def get_organization_by_id(self, organization_id: str) -> Organization:
        """
        Get a single organization by ID.

        Args:
            organization_id (str): The ID of the organization to get.

        Raises:
            HTTPException: If the organization is not found (404).

        Returns:
            Organization: The organization, if found.
        """
        db_org = db.query(Organization).filter(Organization.organization_id == str(organization_id)).first()
        if not db_org:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found.")
        return db_org

    def get_organizations(self, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a list of organizations.

        Args:
            skip (int, optional): The number of organizations to skip. Defaults to 0.
            limit (int, optional): The maximum number of organizations to return. Defaults to 100.

        Returns:
            dict: A dictionary containing the list of organizations and the total count.
        """
        organizations = db.query(Organization).offset(skip).limit(limit).all()
        total = db.query(Organization).count()
        return {"organizations": organizations, "total": total}

    def create_organization(self, organization: OrganizationCreate) -> Organization:
        """
        Create a new organization.

        Args:
            organization (OrganizationCreate): The data to create the organization with.

        Raises:
            HTTPException: If there is an error creating the organization (500).

        Returns:
            Organization: The created organization.
        """
        try:
            existing_org = db.query(Organization).filter(Organization.organization_name == organization.organization_name).first()
            if existing_org:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Organization with this name already exists.")
            db_org = Organization(**organization.dict())
            db.add(db_org)
            db.commit()
            db.refresh(db_org)
            return db_org
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error creating organization: {e}")

    def update_organization(self, organization_id: str, organization: OrganizationUpdate) -> Organization:
        """
        Update an existing organization.

        Args:
            organization_id (str): The ID of the organization to update.
            organization (OrganizationUpdate): The data to update the organization with.

        Raises:
            HTTPException: If the organization is not found (404) or if there is an error updating the organization (500).

        Returns:
            Organization: The updated organization.
        """
        try:
            db_org = self.get_organization_by_id(organization_id)
            update_data = organization.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_org, key, value)
            db_org.updated_at = datetime.utcnow() # Explicitly update the timestamp
            db.commit()
            db.refresh(db_org)
            return db_org
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error updating organization: {e}")

    def delete_organization(self, organization_id: str) -> Organization:
        """
        Soft delete an organization by setting is_active to False.

        Args:
            organization_id (str): The ID of the organization to delete.

        Returns:
            Organization: The updated organization.
        """
        try:
            db_org = self.get_organization_by_id(organization_id)
            db_org.is_active = False
            db_org.updated_at = datetime.utcnow()  # Update timestamp
            db.commit()
            db.refresh(db_org)
            return db_org
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error deleting organization: {e}")