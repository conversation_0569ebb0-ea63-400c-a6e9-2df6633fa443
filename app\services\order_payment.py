from fastapi import HTTPException, status
from datetime import datetime
from sqlalchemy.orm import Session
from models import OrderPayment, Order
from schemas.order_payment import OrderPaymentCreate, OrderPaymentUpdate
from config import db
from decimal import Decimal

class OrderPaymentService:
    def get_order_payment_by_id(self, payment_id: str) -> OrderPayment:
        """
        Get a single order payment by ID.

        Args:
            payment_id (str): The ID of the order payment to get.

        Raises:
            HTTPException: If the order payment is not found (404).

        Returns:
            OrderPayment: The order payment, if found.
        """
        db_order_payment = db.query(OrderPayment).filter(OrderPayment.payment_id == payment_id).first()
        if not db_order_payment:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Order payment not found.")
        return db_order_payment

    def get_order_payments_by_order_id(self, order_id: str, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a list of order payments by order ID.

        Args:
            order_id (str): The ID of the order.
            skip (int, optional): The number of order payments to skip. Defaults to 0.
            limit (int, optional): The maximum number of order payments to return. Defaults to 100.

        Returns:
            dict: A dictionary containing the list of order payments and the total count.
        """
        order_payments = db.query(OrderPayment).filter(OrderPayment.order_id == order_id).offset(skip).limit(limit).all()
        total = db.query(OrderPayment).filter(OrderPayment.order_id == order_id).count()
        return {"order_payments": order_payments, "total": total}

    def create_order_payment(self, order_payment: OrderPaymentCreate) -> OrderPayment:
        """
        Create a new order payment.

        Args:
            order_payment (OrderPaymentCreate): The data to create the order payment with.

        Raises:
            HTTPException: If the order is not found (404) or if there is an error creating the order payment (500).

        Returns:
            OrderPayment: The created order payment.
        """
        db_order = db.query(Order).filter(Order.order_id == order_payment.order_id).first()
        if not db_order:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Order with id {order_payment.order_id} not found.")

        try:
            db_order_payment = OrderPayment(
                **order_payment.dict(),
                payment_status="pending",
            )

            db.add(db_order_payment)
            db.commit()
            db.refresh(db_order_payment)

            return db_order_payment
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error creating order payment: {e}")

    def update_order_payment(self, payment_id: str, order_payment: OrderPaymentUpdate) -> OrderPayment:
        """
        Update an existing order payment.

        Args:
            payment_id (str): The ID of the order payment to update.
            order_payment (OrderPaymentUpdate): The data to update the order payment with.

        Raises:
            HTTPException: If the order payment is not found (404) or if there is an error updating the order payment (500).

        Returns:
            OrderPayment: The updated order payment.
        """
        try:
            db_order_payment = self.get_order_payment_by_id(payment_id)
            update_data = order_payment.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_order_payment, key, value)
            db_order_payment.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_order_payment)
            return db_order_payment
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error updating order payment: {e}")