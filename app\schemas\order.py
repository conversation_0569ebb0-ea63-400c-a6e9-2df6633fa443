from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from enum import Enum


class PricingModel(Enum):
    PER_UNIT = "per_unit"
    SUBSCRIPTION = "subscription"
    ONE_TIME = "one_time"


class OrderStatus(Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ItemType(Enum):
    PRODUCT = "product"
    SERVICE = "service"


class OrderItemCreate(BaseModel):
    item_id: str
    quantity: int


class OrderCreate(BaseModel):
    items: List[OrderItemCreate]


class OrderItem(BaseModel):
    order_item_id: int
    order_id: str
    seller_id: str
    item_id: str
    item_type: str
    quantity: int
    unit_price: Decimal
    sub_total: Decimal
    vat: Decimal
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OrderUpdate(BaseModel):
    order_status: Optional[str] = None
    is_active: Optional[bool] = None


class Order(BaseModel):
    order_id: str
    order_status: str
    buyer_id: str
    currency: str
    sub_total: Decimal
    vat_total: Decimal
    total_amount: Decimal
    is_active: bool
    created_at: datetime
    updated_at: datetime
    items: List[OrderItem] = []


    class Config:
        from_attributes = True


class OrderList(BaseModel):
    orders: List[Order]
    total: int


    class Config:
        from_attributes = True