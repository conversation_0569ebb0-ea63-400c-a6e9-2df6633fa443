from typing import Annotated
from datetime import timedelta
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr

from schemas.users import User, Token
from services.auth import AuthService, ACCESS_TOKEN_EXPIRE_MINUTES
from services.auth import get_current_active_user

router = APIRouter()

class EmailAuthRequest(BaseModel):
    email: EmailStr


@router.post(
    "/token",
    response_model=Token,
    summary="Login for access token"
)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()]
):
    """Login for access token"""
    user = AuthService.authenticate_user(
        form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = AuthService.create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}


@router.post(
    "/token/refresh",
    response_model=Token,
    summary="Refresh access token"
)
async def refresh_access_token(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Refresh access token"""
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = AuthService.create_access_token(
        data={"sub": current_user.email}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}


@router.post(
    "/token/email",
    response_model=Token,
    summary="Get access token using email only"
)
async def get_token_from_email(
    email_data: EmailAuthRequest
):
    """Get access token using email only"""
    user = AuthService.get_user(email_data.email)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = AuthService.create_token_from_email(
        email=email_data.email,
        expires_delta=access_token_expires
    )

    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return {"access_token": access_token, "token_type": "bearer"}
