from fastapi import APIRouter, Depends
from schemas.organization_item import OrganizationItem, OrganizationItemCreate, OrganizationItemUpdate, OrganizationItemList
from schemas.users import User
from services.organization_item import OrganizationItemService
from services.auth import get_current_user

router = APIRouter()
current_user = Depends(get_current_user)

@router.post(
    "/create", 
    response_model=OrganizationItem, 
    summary="Create a new organization item"
)
def create_organization_item(
    item: OrganizationItemCreate,
    current_user: User = current_user,
):
    """
    Create a new organization item.
    """
    response = OrganizationItemService().create_organization_item(item=item)
    return response

@router.get(
    "/{item_id}", 
    response_model=OrganizationItem, 
    summary="Read a single organization item"
)
def read_organization_item(
    item_id: str,
    current_user: User = current_user,
):
    """
    Read a single organization item.
    """
    db_item = OrganizationItemService().get_organization_item_by_id(item_id=item_id)
    return db_item

@router.get(
    "/", 
    response_model=OrganizationItemList, 
    summary="Read a list of all items"
)
def read_all_organization_items(
    skip: int = 0, 
    limit: int = 100,
    current_user: User = current_user,
):
    """
    Read a list of organization items.
    """
    response = OrganizationItemService().get_all_organization_items(skip=skip, limit=limit)
    return response

@router.get(
    "/organization/{organization_id}", 
    response_model=OrganizationItemList, 
    summary="Read a list of organization items by organization ID"
)
def read_organization_items_by_organization_id(
    organization_id: str,
    skip: int = 0, 
    limit: int = 100,
    current_user: User = current_user,
):
    """
    Read a list of organization items by organization ID.
    """
    response = OrganizationItemService().get_organization_items_by_organization_id(organization_id=organization_id, skip=skip, limit=limit)
    return response

@router.put(
    "/{item_id}", 
    response_model=OrganizationItem, 
    summary="Update an organization item"
)
def update_organization_item(
    item_id: str, 
    item: OrganizationItemUpdate,
    current_user: User = current_user,
):
    """
    Update an organization item.
    """
    db_item = OrganizationItemService().update_organization_item(item_id=item_id, item=item)
    return db_item

@router.delete(
    "/{item_id}", 
    response_model=OrganizationItem, 
    summary="Delete an organization item"
)
def delete_organization_item(
    item_id: str,
    current_user: User = current_user,
):
    """
    Delete an organization item.
    """
    db_item = OrganizationItemService().delete_organization_item(item_id=item_id)
    return db_item

