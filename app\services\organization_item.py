from fastapi import HTTPException, status
from datetime import datetime
from sqlalchemy.orm import Session
from models import OrganizationItem
from schemas.organization_item import OrganizationItemCreate, OrganizationItemUpdate
from config import db

class OrganizationItemService:
    def get_organization_item_by_id(self, item_id: str) -> OrganizationItem:
        db_item = db.query(OrganizationItem).filter(OrganizationItem.item_id == item_id).first()
        if not db_item:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization item not found.")
        return db_item

    def get_all_organization_items(self, skip: int = 0, limit: int = 100) -> dict:
        items = db.query(OrganizationItem).offset(skip).limit(limit).all()
        total = db.query(OrganizationItem).count()
        return {"items": items, "total": total}

    def get_organization_items_by_organization_id(self, organization_id: str, skip: int = 0, limit: int = 100) -> dict:
        items = db.query(OrganizationItem).filter(OrganizationItem.organization_id == organization_id).offset(skip).limit(limit).all()
        total = db.query(OrganizationItem).filter(OrganizationItem.organization_id == organization_id).count()
        return {"items": items, "total": total}

    def create_organization_item(self, item: OrganizationItemCreate) -> OrganizationItem:
        try:
            db_item = OrganizationItem(**item.dict())
            db.add(db_item)
            db.commit()
            db.refresh(db_item)
            return db_item
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error creating organization item: {e}")

    def update_organization_item(self, item_id: str, item: OrganizationItemUpdate) -> OrganizationItem:
        try:
            db_item = self.get_organization_item_by_id(item_id)
            update_data = item.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_item, key, value)
            db_item.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_item)
            return db_item
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error updating organization item: {e}")

    def delete_organization_item(self, item_id: str) -> OrganizationItem:
        try:
            db_item = self.get_organization_item_by_id(item_id)
            db_item.is_active = False
            db_item.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_item)
            return db_item
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error deleting organization item: {e}")
